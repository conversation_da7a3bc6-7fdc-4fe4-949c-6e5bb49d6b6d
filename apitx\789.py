import asyncio
import websockets
import json
import threading
import time
from http.server import HTTPServer, BaseHTTPRequestHandler
import os
from urllib.parse import urlparse

PORT = int(os.environ.get('PORT', 10000))

latest_result = {
    "id": "binhtool90", 
    "id_phien": 0,
    "ket_qua": "Chưa có kết quả"
}

# <PERSON><PERSON><PERSON>ch sử kết quả T/X tối đa 20 lần
pattern_history = ""

def update_pattern_history(result):
    global pattern_history
    if len(pattern_history) >= 20:
        pattern_history = pattern_history[1:]
    pattern_history += result

def predict_next_from_pattern(history):
    if len(history) < 6:
        return "Chưa đủ dữ liệu dự đoán"
    last_char = history[-1]
    predicted = 'x' if last_char == 't' else 't'
    return "Tài" if predicted == 't' else "Xỉu"

WS_URL = "wss://websocket.atpman.net/websocket"
HEADERS = {
    "Host": "websocket.atpman.net",
    "Origin": "https://play.789club.sx",
    "User-Agent": "Mozilla/5.0",
    "Accept-Encoding": "gzip, deflate, br, zstd",
    "Accept-Language": "vi-VN,vi;q=0.9",
    "Pragma": "no-cache",
    "Cache-Control": "no-cache"
}

last_event_id = 19

LOGIN_MESSAGE = [
    1, "MiniGame", "apitx789", "binhtool90",
    {
        "info": json.dumps({
            "ipAddress": "2a09:bac5:d44b:16d2::246:d4",
            "userId": "6af5b295-bae8-4c69-8386-afeaafd4101b",
            "username": "S8_apitx789",
            "timestamp": 1751786319973,
            "refreshToken": "6947ef5011a14921b42c70a57239b279.ba8aef3c9b094ec9961dc9c5def594cf"
        }),
        "signature": "47D64C1BB382E32AD40837624A640609370AAD1D67B5B1B51FDE6BB205DD5AB1FCE9A008DF7D7E5DA718F718A1B587B08D228B3F5AE670E8242046B56213AA0B407C4B4AFAC146ACFA24162F11DF5F444CDDDBE3F2CE3439C7F25E5947787CDE863FFE350934133552D2CAFCF5E1DBB1A91BD987254A44479B42F99F0509251F"
    }
]

SUBSCRIBE_TX_RESULT = [6, "MiniGame", "taixiuUnbalancedPlugin", {"cmd": 2000}]
SUBSCRIBE_LOBBY = [6, "MiniGame", "lobbyPlugin", {"cmd": 10001}]

async def connect_websocket():
    global last_event_id, latest_result, pattern_history
    
    while True:
        try:
            async with websockets.connect(WS_URL, extra_headers=HEADERS) as websocket:
                print("✅ Đã kết nối WebSocket")
                
                # Gửi login message
                await websocket.send(json.dumps(LOGIN_MESSAGE))
                
                # Đợi 1 giây rồi subscribe
                await asyncio.sleep(1)
                await websocket.send(json.dumps(SUBSCRIBE_TX_RESULT))
                await websocket.send(json.dumps(SUBSCRIBE_LOBBY))
                
                # Tạo các task cho heartbeat và subscribe định kỳ
                heartbeat_task = asyncio.create_task(heartbeat(websocket))
                subscribe_task = asyncio.create_task(periodic_subscribe(websocket))
                simms_task = asyncio.create_task(periodic_simms(websocket))
                
                # Lắng nghe messages
                async for message in websocket:
                    try:
                        data = json.loads(message)
                        
                        if isinstance(data, list):
                            # Cập nhật last_event_id
                            if (len(data) >= 3 and data[0] == 7 and 
                                data[1] == "Simms" and isinstance(data[2], int)):
                                last_event_id = data[2]
                            
                            # Xử lý kết quả Tài Xỉu
                            if (len(data) >= 2 and isinstance(data[1], dict) and 
                                data[1].get("cmd") == 2006):
                                result_data = data[1]
                                sid = result_data.get("sid")
                                d1 = result_data.get("d1")
                                d2 = result_data.get("d2") 
                                d3 = result_data.get("d3")
                                
                                if all(x is not None for x in [sid, d1, d2, d3]):
                                    tong = d1 + d2 + d3
                                    ketqua = "Tài" if tong >= 11 else "Xỉu"
                                    
                                    latest_result = {
                                        "id": "binhtool90",
                                        "id_phien": sid,
                                        "ket_qua": f"{d1}-{d2}-{d3} = {tong} ({ketqua})"
                                    }
                                    
                                    result_tx = 't' if ketqua == "Tài" else 'x'
                                    update_pattern_history(result_tx)
                                    
                                    print(latest_result)
                                    print("🔮 Dự đoán pattern tiếp theo:", predict_next_from_pattern(pattern_history))
                                    
                    except json.JSONDecodeError:
                        continue
                    except Exception as err:
                        print(f"❌ Lỗi message: {err}")
                        
        except websockets.exceptions.ConnectionClosed:
            print("🔌 WebSocket đóng. Kết nối lại sau 5s...")
            await asyncio.sleep(5)
        except Exception as err:
            print(f"❌ Lỗi WebSocket: {err}")
            await asyncio.sleep(5)

async def heartbeat(websocket):
    while True:
        try:
            await asyncio.sleep(10)
            await websocket.send("2")
        except:
            break

async def periodic_subscribe(websocket):
    while True:
        try:
            await asyncio.sleep(30)
            await websocket.send(json.dumps(SUBSCRIBE_TX_RESULT))
        except:
            break

async def periodic_simms(websocket):
    while True:
        try:
            await asyncio.sleep(15)
            message = [7, "Simms", last_event_id, 0, {"id": 0}]
            await websocket.send(json.dumps(message))
        except:
            break

class HTTPRequestHandler(BaseHTTPRequestHandler):
    def do_OPTIONS(self):
        self.send_response(204)
        self.send_header("Access-Control-Allow-Origin", "*")
        self.send_header("Access-Control-Allow-Methods", "GET, OPTIONS")
        self.send_header("Access-Control-Allow-Headers", "Content-Type")
        self.end_headers()
        
    def do_GET(self):
        parsed_url = urlparse(self.path)
        
        if parsed_url.path == "/taixiu":
            self.send_response(200)
            self.send_header("Content-Type", "application/json; charset=utf-8")
            self.send_header("Access-Control-Allow-Origin", "*")
            self.send_header("Access-Control-Allow-Methods", "GET, OPTIONS")
            self.send_header("Access-Control-Allow-Headers", "Content-Type")
            self.end_headers()
            
            response_data = {
                "latestResult": latest_result,
                "patternHistory": pattern_history,
                "duDoanPattern": predict_next_from_pattern(pattern_history)
            }
            
            self.wfile.write(json.dumps(response_data, ensure_ascii=False).encode('utf-8'))
        else:
            self.send_response(404)
            self.send_header("Content-Type", "text/plain; charset=utf-8")
            self.send_header("Access-Control-Allow-Origin", "*")
            self.send_header("Access-Control-Allow-Methods", "GET, OPTIONS")
            self.send_header("Access-Control-Allow-Headers", "Content-Type")
            self.end_headers()
            self.wfile.write("Không tìm thấy".encode('utf-8'))
    
    def log_message(self, format, *args):
        # Tắt log mặc định của HTTP server
        pass

def start_http_server():
    server = HTTPServer(('', PORT), HTTPRequestHandler)
    print(f"🌐 Server đang chạy tại http://localhost:{PORT}")
    server.serve_forever()

def start_websocket_client():
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    loop.run_until_complete(connect_websocket())

if __name__ == "__main__":
    # Chạy WebSocket client trong thread riêng
    websocket_thread = threading.Thread(target=start_websocket_client, daemon=True)
    websocket_thread.start()
    
    # Chạy HTTP server trong main thread
    start_http_server()