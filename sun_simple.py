import websocket
import threading
import time
import json
import os
from flask import Flask, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)
PORT = int(os.environ.get('PORT', 5000))

# === Biến lưu trạng thái ===
current_data = {
    "id": "binhtool90",
    "id_phien": None,
    "ket_qua": "Đang kết nối...",
    "pattern": "",
    "du_doan": "?",
    "connection_status": "Connecting..."
}

pattern_history = []
ws = None
ws_connected = False

def du_doan_tiep_theo(pattern):
    """Dự đoán kết quả tiếp theo dựa trên pattern"""
    if len(pattern) < 3:
        return "?"
    
    # Logic dự đoán đơn giản
    last = pattern[-1]
    return "X" if last == "T" else "T"

def on_message(ws, message):
    """Xử lý tin nhắn từ WebSocket"""
    global current_data, pattern_history, ws_connected
    
    try:
        print(f"[📨] Nhận: {message[:200]}...")
        
        # Parse JSON
        data = json.loads(message)
        
        # Ki<PERSON><PERSON> tra các định dạng message khác nhau
        if isinstance(data, list) and len(data) > 1:
            if isinstance(data[1], dict):
                cmd = data[1].get('cmd')
                
                # Xử lý kết quả tài xỉu
                if cmd == 1003 and 'gBB' in data[1]:
                    d1 = data[1].get('d1', 0)
                    d2 = data[1].get('d2', 0) 
                    d3 = data[1].get('d3', 0)
                    total = d1 + d2 + d3
                    result = "T" if total > 10 else "X"
                    
                    pattern_history.append(result)
                    if len(pattern_history) > 20:
                        pattern_history.pop(0)
                    
                    ket_qua = f"{d1}-{d2}-{d3} = {total} ({'Tài' if result == 'T' else 'Xỉu'})"
                    du_doan = du_doan_tiep_theo(pattern_history)
                    
                    current_data.update({
                        "ket_qua": ket_qua,
                        "pattern": "".join(pattern_history),
                        "du_doan": "Tài" if du_doan == "T" else "Xỉu",
                        "connection_status": "Connected"
                    })
                    
                    print(f"[🎯] Kết quả: {ket_qua} | Dự đoán: {current_data['du_doan']}")
                
                # Xử lý phiên mới
                elif cmd == 1008 and 'sid' in data[1]:
                    current_data["id_phien"] = data[1]['sid']
                    print(f"[📋] Phiên mới: {data[1]['sid']}")
        
        # Cập nhật trạng thái kết nối
        current_data["connection_status"] = "Connected"
        
    except Exception as e:
        print(f"[❌] Lỗi xử lý message: {e}")

def on_error(ws, error):
    """Xử lý lỗi WebSocket"""
    global current_data
    print(f"[❌] WebSocket lỗi: {error}")
    current_data["connection_status"] = f"Error: {error}"

def on_close(ws, close_status_code, close_msg):
    """Xử lý khi WebSocket đóng"""
    global ws_connected, current_data
    ws_connected = False
    current_data["connection_status"] = "Disconnected"
    print(f"[🔌] WebSocket đóng: {close_status_code} - {close_msg}")
    
    # Thử kết nối lại sau 5 giây
    time.sleep(5)
    connect_websocket()

def on_open(ws):
    """Xử lý khi WebSocket mở"""
    global ws_connected, current_data
    ws_connected = True
    current_data["connection_status"] = "Connected"
    print("[✅] WebSocket kết nối thành công!")
    
    # Gửi tin nhắn để đăng ký nhận data tài xỉu
    try:
        # Message để join tài xỉu room
        messages = [
            [6, "MiniGame", "taixiuPlugin", {"cmd": 1005}],
            [6, "MiniGame", "lobbyPlugin", {"cmd": 10001}]
        ]
        
        for i, msg in enumerate(messages):
            time.sleep(1)
            ws.send(json.dumps(msg))
            print(f"[📤] Gửi: {msg}")
            
    except Exception as e:
        print(f"[❌] Lỗi gửi message: {e}")

def connect_websocket():
    """Kết nối WebSocket"""
    global ws
    
    try:
        print("[🔄] Đang kết nối WebSocket...")
        
        # Sử dụng URL từ file JavaScript gốc
        ws_url = "wss://websocket.azhkthg1.net/websocket"
        
        ws = websocket.WebSocketApp(
            ws_url,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close,
            on_open=on_open,
            header={
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "Origin": "https://play.sun.win"
            }
        )
        
        # Chạy WebSocket trong thread riêng
        def run_ws():
            ws.run_forever(ping_interval=30, ping_timeout=10)
        
        ws_thread = threading.Thread(target=run_ws, daemon=True)
        ws_thread.start()
        
    except Exception as e:
        print(f"[❌] Lỗi kết nối WebSocket: {e}")

# === API Routes ===
@app.route('/taixiu')
def get_taixiu():
    """API endpoint trả về dữ liệu tài xỉu"""
    return jsonify(current_data)

@app.route('/')
def home():
    """Trang chủ"""
    return f'''
    <h2>🎯 Sunwin Tài Xỉu Monitor</h2>
    <p><strong>Status:</strong> {current_data["connection_status"]}</p>
    <p><strong>Kết quả:</strong> {current_data["ket_qua"]}</p>
    <p><strong>Pattern:</strong> {current_data["pattern"]}</p>
    <p><strong>Dự đoán:</strong> {current_data["du_doan"]}</p>
    <p><a href="/taixiu">Xem JSON</a></p>
    <script>
        setTimeout(() => location.reload(), 5000);
    </script>
    '''

@app.route('/status')
def status():
    """Endpoint kiểm tra trạng thái"""
    return jsonify({
        "websocket_connected": ws_connected,
        "pattern_length": len(pattern_history),
        "last_patterns": pattern_history[-10:] if pattern_history else []
    })

if __name__ == '__main__':
    print(f"[🌐] Server khởi động tại http://localhost:{PORT}")
    
    # Khởi động WebSocket trong thread riêng
    threading.Thread(target=connect_websocket, daemon=True).start()
    
    # Khởi động Flask server
    app.run(host='0.0.0.0', port=PORT, debug=False)