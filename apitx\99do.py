import requests
import time
from collections import defaultdict, Counter

# === Cấu hình API ===
url = "https://rickapi.store/tx/api/GetListSoiCau"
headers = {
    "Authorization": "Bearer i6SGNKiSQS8jVTwmZZdqwL8BBCQZWQFMqgb9J3gBG7uwdZiTlsGd2rxCjfcfYf7rqt/mtHcJUhD438z3ryi5QYaPzMoSsOP/zJQ8K8kbY7H/JD4dIYtSoCFJjTVl69f8PjndW1TzKWib6mieSmW4AQlyUMkvyC+Zsq2BzY4duAI=",
    "Origin": "https://99do.club",
    "Referer": "https://99do.club/",
    "Content-Type": "application/json",
    "Accept": "application/json"
}
INTERVAL_SECONDS = 10
last_session_id = None
notified_no_new_session = False

# === Biến toàn cục ===
history = []

# === Chuyển tổng thành kết quả T/X ===
def result_to_tx(total):
    return "T" if total >= 11 else "X"

# === Lấy danh sách xúc xắc từ API ===
def extract_dice_from_data(data):
    return [(d["Dice1"], d["Dice2"], d["Dice3"]) for d in reversed(data)]

# === Dự đoán kết hợp nâng cao ===
def predict_next(history, max_window=7):
    n = len(history)
    if n < 3:
        # Quá ít dữ liệu, dựa trên thống kê gần nhất
        count = Counter(history)
        pred = max(count, key=count.get)
        confidence = count[pred] / len(history)
        return pred, confidence, "Thống kê gần nhất (dữ liệu ít)"

    weighted_votes = defaultdict(float)
    total_weight = 0.0
    prediction_reason = ""

    # Tìm mẫu lặp từ window lớn đến nhỏ (ưu tiên mẫu dài và gần nhất)
    for window in range(min(max_window, n-1), 1, -1):
        pattern = "".join(history[-window:])
        counter = Counter()
        positions = []

        for i in range(n - window):
            if "".join(history[i:i+window]) == pattern:
                next_pos = i + window
                if next_pos < n:
                    counter[history[next_pos]] += 1
                    positions.append(next_pos)

        if counter:
            # Tính trọng số theo khoảng cách gần nhất
            weighted_counter = defaultdict(float)
            for outcome in counter:
                weighted_sum = 0.0
                for pos in positions:
                    if pos < n and history[pos] == outcome:
                        weight = 1 / (n - pos)  # Gần hơn trọng số lớn hơn
                        weighted_sum += weight
                weighted_counter[outcome] = weighted_sum

            pred = max(weighted_counter, key=weighted_counter.get)
            confidence = weighted_counter[pred] / sum(weighted_counter.values())
            prediction_reason = f"Mẫu lặp '{pattern}' với trọng số"
            return pred, confidence, prediction_reason

    # Cầu bệt mạnh: 5 phiên giống nhau
    if n >= 5 and len(set(history[-5:])) == 1:
        return history[-1], 0.9, "Cầu bệt mạnh (5 phiên giống nhau)"

    # Cầu xen kẽ mở rộng (ví dụ T-X-T-X-T hoặc X-T-X-T-X)
    if n >= 6:
        last6 = history[-6:]
        if all(last6[i] != last6[i+1] for i in range(5)):
            next_tx = "T" if last6[-1] == "X" else "X"
            return next_tx, 0.8, "Cầu xen kẽ mở rộng (6 phiên gần nhất)"

    # Chu kỳ (cycle) đơn giản: kiểm tra chu kỳ 2-3 phiên lặp lại
    for cycle_len in range(2, 4):
        if n >= cycle_len * 2:
            cycle = history[-cycle_len:]
            repeated = True
            for i in range(1, n // cycle_len):
                if history[-(i+1)*cycle_len : -i*cycle_len] != cycle:
                    repeated = False
                    break
            if repeated:
                next_idx = n % cycle_len
                pred = cycle[next_idx]
                return pred, 0.85, f"Phát hiện chu kỳ lặp lại (chu kỳ {cycle_len})"

    # Xu hướng nghiêng rõ rệt trong 8 phiên
    recent = history[-8:]
    count = Counter(recent)
    if abs(count["T"] - count["X"]) >= 5:
        pred = "T" if count["T"] > count["X"] else "X"
        confidence = count[pred] / 8
        return pred, confidence, "Xu hướng nghiêng rõ rệt (8 phiên gần nhất)"

    # Fallback: kết hợp thống kê gần nhất và xu hướng dài hơn
    count_all = Counter(history)
    pred = max(count_all, key=count_all.get)
    confidence = count_all[pred] / n
    return pred, confidence, "Fallback: Thống kê tổng thể"

# === Gọi API để lấy dữ liệu mới ===
def fetch_data():
    try:
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ Lỗi khi gọi API: {response.status_code}")
            return None
    except Exception as e:
        print(f"⚠️ Lỗi kết nối: {e}")
        return None

# === Kiểm tra và xử lý phiên mới ===
def check_for_new_session(data):
    global last_session_id, notified_no_new_session, history

    if data and isinstance(data, list) and len(data) > 0:
        latest_session = data[0]
        current_session_id = latest_session["GameSessionID"]

        if current_session_id != last_session_id:
            print("\n🔥 PHÁT HIỆN PHIÊN MỚI!")
            print(f"🎲 Phiên: {latest_session['GameSessionID']} | Tổng: {latest_session['DiceSum']}")

            dice_list = extract_dice_from_data(data)
            totals = [sum(d) for d in dice_list]
            history = [result_to_tx(t) for t in totals]

            prediction, confidence, reason = predict_next(history)

            print(f"📊 Cơ sở dự đoán: {reason}")
            print(f"🤖 Dự đoán: {prediction} | Độ tin cậy: {confidence:.2%}")

            last_session_id = current_session_id
            notified_no_new_session = False
        else:
            if not notified_no_new_session:
                print("-" * 42)
                print("⏳ Chưa có phiên mới.")
                notified_no_new_session = True
    else:
        print("⚠️ Không nhận được dữ liệu hợp lệ.")

# === Vòng lặp chính ===
if __name__ == "__main__":
    print("🚀 Đang theo dõi và dự đoán Tài/Xỉu bằng mô hình thông minh...")
    print("CỔNG GAME 99do.club")
    while True:
        data = fetch_data()
        check_for_new_session(data)
        time.sleep(INTERVAL_SECONDS)
