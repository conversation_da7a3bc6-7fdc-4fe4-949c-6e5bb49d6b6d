import random
import time
import threading
from flask import Flask, jsonify
from flask_cors import CORS

app = Flask(__name__)
CORS(app)

# === Dữ liệu giả lập ===
current_data = {
    "id": "binhtool90",
    "id_phien": None,
    "ket_qua": "",
    "pattern": "",
    "du_doan": "?"
}

pattern_history = []
phien_counter = 1000

def generate_fake_result():
    """Tạo kết quả tài xỉu giả lập"""
    global current_data, pattern_history, phien_counter
    
    # Tạo 3 số xúc xắc ngẫu nhiên
    d1 = random.randint(1, 6)
    d2 = random.randint(1, 6) 
    d3 = random.randint(1, 6)
    total = d1 + d2 + d3
    
    # Xác định Tài/Xỉu
    result = "T" if total > 10 else "X"
    
    # Lưu vào pattern
    pattern_history.append(result)
    if len(pattern_history) > 20:
        pattern_history.pop(0)
    
    # Dự đo<PERSON> đơn giản (ngư<PERSON><PERSON> lại với kết quả cuối)
    du_doan = "X" if result == "T" else "T"
    
    # Cập nhật dữ liệu
    phien_counter += 1
    ket_qua = f"{d1}-{d2}-{d3} = {total} ({'Tài' if result == 'T' else 'Xỉu'})"
    
    current_data.update({
        "id_phien": phien_counter,
        "ket_qua": ket_qua,
        "pattern": "".join(pattern_history),
        "du_doan": "Tài" if du_doan == "T" else "Xỉu"
    })
    
    print(f"[🎯] Phiên {phien_counter}: {ket_qua} | Pattern: {current_data['pattern']} | Dự đoán: {current_data['du_doan']}")

def auto_generate_results():
    """Tự động tạo kết quả mới mỗi 30 giây"""
    while True:
        time.sleep(30)  # Mỗi 30 giây một phiên
        generate_fake_result()

# === API Routes ===
@app.route('/taixiu')
def get_taixiu():
    """API endpoint trả về dữ liệu tài xỉu"""
    return jsonify(current_data)

@app.route('/')
def home():
    """Trang chủ với giao diện đẹp"""
    return f'''
    <!DOCTYPE html>
    <html>
    <head>
        <title>🎯 Sunwin Tài Xỉu</title>
        <meta charset="utf-8">
        <style>
            body {{ font-family: Arial; margin: 40px; background: #f0f2f5; }}
            .container {{ max-width: 600px; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
            .title {{ color: #e74c3c; text-align: center; margin-bottom: 30px; }}
            .result {{ font-size: 24px; font-weight: bold; color: #2c3e50; margin: 20px 0; }}
            .pattern {{ background: #ecf0f1; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 18px; }}
            .prediction {{ font-size: 20px; color: #27ae60; font-weight: bold; }}
            .refresh {{ color: #3498db; }}
            .api-link {{ display: inline-block; margin-top: 20px; padding: 10px 20px; background: #3498db; color: white; text-decoration: none; border-radius: 5px; }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="title">🎯 Sunwin Tài Xỉu Monitor</h1>
            
            <div class="result">
                <strong>Kết quả hiện tại:</strong><br>
                {current_data["ket_qua"] or "Chưa có dữ liệu"}
            </div>
            
            <div class="pattern">
                <strong>Pattern gần đây:</strong><br>
                {current_data["pattern"] or "Chưa có pattern"}
            </div>
            
            <div class="prediction">
                <strong>🔮 Dự đoán phiên tiếp:</strong> {current_data["du_doan"]}
            </div>
            
            <div class="refresh">
                <p>⏰ Tự động làm mới mỗi 5 giây</p>
                <p>📊 Phiên số: {current_data["id_phien"] or "N/A"}</p>
            </div>
            
            <a href="/taixiu" class="api-link">📡 Xem JSON API</a>
        </div>
        
        <script>
            // Tự động làm mới trang mỗi 5 giây
            setTimeout(() => location.reload(), 5000);
        </script>
    </body>
    </html>
    '''

@app.route('/generate')
def manual_generate():
    """Endpoint để tạo kết quả thủ công"""
    generate_fake_result()
    return jsonify({"message": "Đã tạo kết quả mới!", "data": current_data})

if __name__ == '__main__':
    print("🚀 Sunwin Tài Xỉu Simulator")
    print("📊 Tạo kết quả giả lập mỗi 30 giây")
    print(f"🌐 Truy cập: http://localhost:5000")
    print("🎯 API: http://localhost:5000/taixiu")
    print("⚡ Tạo thủ công: http://localhost:5000/generate")
    
    # Tạo kết quả đầu tiên
    generate_fake_result()
    
    # Khởi động thread tự động tạo kết quả
    threading.Thread(target=auto_generate_results, daemon=True).start()
    
    # Khởi động Flask server
    app.run(host='0.0.0.0', port=5000, debug=False)