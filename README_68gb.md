# Hướng dẫn sử dụng 68gb.py

## <PERSON>ô tả
File `68gb.py` là bản chuyển đổi từ JavaScript sang Python của `68gb.js`. Chương trình này theo dõi WebSocket connections và gửi dữ liệu game lên Firebase Realtime Database.

## Cài đặt

### 1. Cài đặt Python
Đảm bảo bạn đã cài Python 3.7 trở lên.

### 2. Cài đặt thư viện
```bash
pip install -r requirements.txt
```

Hoặc cài đặt từng thư viện:
```bash
pip install websockets requests
```

## Cấu hình

### 1. Cập nhật URL WebSocket
Trong file `68gb.py`, tìm dòng:
```python
WEBSOCKET_URL = "wss://example-game-server.com/websocket"  # ⚠️ CẦN CẬP NHẬT URL THỰC
```

Thay thế bằng URL WebSocket thực tế của game 68GB.

### 2. <PERSON><PERSON><PERSON> tra URL Firebase
URL Firebase đã được cấu hình sẵn:
```python
FIREBASE_DB_URL = "https://data-real-time-68gb-default-rtdb.asia-southeast1.firebasedatabase.app"
```

## Sử dụng

### Chạy chương trình
```bash
python 68gb.py
```

### Tính năng
- ✅ Theo dõi WebSocket real-time
- ✅ Tự động kết nối lại khi mất kết nối
- ✅ Lọc và xử lý messages chứa "mnmdsbgamestart" và "mnmdsbgameend"
- ✅ Gửi dữ liệu lên Firebase với timestamp
- ✅ Logging chi tiết và xử lý lỗi
- ✅ Hỗ trợ cả text và binary messages

### Logs
Chương trình sẽ ghi log vào:
- Console (stdout)
- File `68gb_monitor.log`

## Khác biệt với JavaScript version

| JavaScript | Python |
|------------|---------|
| Ghi đè WebSocket của browser | WebSocket client độc lập |
| Chạy trong browser | Chạy như script Python |
| Bị động theo dõi | Chủ động kết nối |
| Phụ thuộc vào browser | Độc lập hoàn toàn |

## Dừng chương trình
Nhấn `Ctrl+C` để dừng chương trình một cách an toàn.

## Troubleshooting

### Lỗi kết nối WebSocket
- Kiểm tra URL WebSocket có đúng không
- Đảm bảo server WebSocket đang hoạt động
- Kiểm tra firewall/proxy settings

### Lỗi gửi Firebase
- Kiểm tra kết nối internet
- Xác minh URL Firebase Database
- Kiểm tra quyền truy cập Firebase

### Lỗi import
- Cài đặt lại thư viện: `pip install -r requirements.txt`
- Kiểm tra phiên bản Python: `python --version`