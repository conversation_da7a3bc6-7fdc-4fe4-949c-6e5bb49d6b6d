import asyncio
import websockets
import json
import time
import logging
from flask import Flask, jsonify
from flask_cors import CORS
import threading
import requests

# === Cấu hình logging ===
logging.basicConfig(level=logging.INFO, format='[%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

# === Biến lưu trạng thái ===
current_data = {
    "id": "binhtool90",
    "id_phien": None,
    "ket_qua": "",
    "pattern": "",
    "du_doan": "?"
}
id_phien_chua_co_kq = None
pattern_history = []  # Lưu dãy T/X gần nhất

# === Danh sách tin nhắn gửi lên server WebSocket ===
messages_to_send = [
    [1, "MiniGame", "SC_dsucac", "binhsex", {
        "info": "{\"ipAddress\":\"\",\"userId\":\"\",\"username\":\"\",\"timestamp\":,\"refreshToken\":\"\"}",
        "signature": ""
    }],
    [6, "MiniGame", "taixiuPlugin", {"cmd": 1005}],
    [6, "MiniGame", "lobbyPlugin", {"cmd": 10001}]
]

# === WebSocket ===
websocket = None
ping_interval = None
reconnect_timeout = None
is_manually_closed = False

def du_doan_tiep_theo(pattern):
    """Thuật toán dự đoán tiếp theo"""
    if len(pattern) < 6:
        return "?"
    
    last3 = ''.join(pattern[-3:])
    last4 = ''.join(pattern[-4:])
    
    # Kiểm tra nếu 3 ký tự cuối lặp (vd: TXT → TXT)
    pattern_str = ''.join(pattern)
    count = pattern_str.count(last3)
    if count >= 2:
        return last3[0]  # đoán tiếp theo là chữ đầu chuỗi
    
    # Nếu thấy lặp 2 lần gần đây
    count4 = pattern_str.count(last4)
    if count4 >= 2:
        return last4[0]
    
    return "?"

async def connect_websocket():
    """Kết nối WebSocket"""
    global websocket, id_phien_chua_co_kq, current_data, pattern_history
    
    uri = "wss://websocket.azhkthg1.net/wsbinary?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Vs9JDMd9hRNIFV3BcB74NV0W8YK55KKwM9xA8OUP9xA
"
    
    headers = {
        "User-Agent": "Mozilla/5.0",
        "Origin": "https://web.sunwin.pro"
    }
    
    try:
        async with websockets.connect(uri, extra_headers=headers) as ws:
            websocket = ws
            logger.info("[✅] WebSocket kết nối")
            
            # Gửi các tin nhắn ban đầu
            for i, msg in enumerate(messages_to_send):
                await asyncio.sleep(i * 0.6)  # 600ms delay
                await ws.send(json.dumps(msg))
            
            # Ping interval
            async def ping_task():
                while True:
                    await asyncio.sleep(15)  # 15 giây
                    if ws.open:
                        await ws.ping()
                        logger.info("[📶] Ping OK")
            
            ping_task_handle = asyncio.create_task(ping_task())
            
            # Lắng nghe tin nhắn
            async for message in ws:
                try:
                    data = json.loads(message)
                    if isinstance(data, list) and len(data) > 1 and isinstance(data[1], dict):
                        cmd = data[1].get('cmd')
                        
                        if cmd == 1008 and 'sid' in data[1]:
                            id_phien_chua_co_kq = data[1]['sid']
                        
                        if cmd == 1003 and 'gBB' in data[1]:
                            d1 = data[1].get('d1', 0)
                            d2 = data[1].get('d2', 0)
                            d3 = data[1].get('d3', 0)
                            total = d1 + d2 + d3
                            result = "T" if total > 10 else "X"  # Tài / Xỉu
                            
                            # Lưu pattern
                            pattern_history.append(result)
                            if len(pattern_history) > 20:
                                pattern_history.pop(0)
                            
                            text = f"{d1}-{d2}-{d3} = {total} ({'Tài' if result == 'T' else 'Xỉu'})"
                            
                            # Dự đoán
                            du_doan = du_doan_tiep_theo(pattern_history)
                            
                            current_data = {
                                "id": "binhtool90",
                                "id_phien": id_phien_chua_co_kq,
                                "ket_qua": text,
                                "pattern": ''.join(pattern_history),
                                "du_doan": "Tài" if du_doan == "T" else "Xỉu" if du_doan == "X" else "?"
                            }
                            
                            logger.info(f"Phiên {id_phien_chua_co_kq}: {text} → Dự đoán tiếp: {current_data['du_doan']}")
                            id_phien_chua_co_kq = None
                            
                except json.JSONDecodeError as e:
                    logger.error(f"[Lỗi JSON]: {e}")
                except Exception as e:
                    logger.error(f"[Lỗi]: {e}")
            
            ping_task_handle.cancel()
            
    except websockets.exceptions.ConnectionClosed:
        logger.info("[🔌] WebSocket ngắt. Đang kết nối lại...")
        if not is_manually_closed:
            await asyncio.sleep(2.5)
            await connect_websocket()
    except Exception as e:
        logger.error(f"[❌] WebSocket lỗi: {e}")
        if not is_manually_closed:
            await asyncio.sleep(2.5)
            await connect_websocket()

# === Flask API ===
app = Flask(__name__)
CORS(app)

@app.route('/taixiu', methods=['GET'])
def get_taixiu():
    """API trả về kết quả Tài Xỉu"""
    return jsonify(current_data)

@app.route('/', methods=['GET'])
def home():
    """Trang chủ"""
    return """
    <h2>🎯 Kết quả Sunwin Tài Xỉu</h2>
    <p><a href="/taixiu">Xem kết quả JSON</a></p>
    <p><a href="/status">Xem trạng thái</a></p>
    """

@app.route('/status', methods=['GET'])
def status():
    """API trả về trạng thái hệ thống"""
    return jsonify({
        "status": "running",
        "pattern_length": len(pattern_history),
        "last_update": time.time(),
        "websocket_connected": websocket is not None and websocket.open if websocket else False
    })

def run_websocket():
    """Chạy WebSocket trong thread riêng"""
    asyncio.set_event_loop(asyncio.new_event_loop())
    asyncio.get_event_loop().run_until_complete(connect_websocket())

def run_flask():
    """Chạy Flask server"""
    port = int(os.environ.get('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=False)

if __name__ == "__main__":
    import os
    
    # Chạy WebSocket trong thread riêng
    websocket_thread = threading.Thread(target=run_websocket, daemon=True)
    websocket_thread.start()
    
    # Chạy Flask server
    logger.info(f"[🌐] Server chạy tại http://localhost:{os.environ.get('PORT', 5000)}")
    run_flask()
