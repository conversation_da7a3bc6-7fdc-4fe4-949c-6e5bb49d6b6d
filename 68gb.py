#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket Monitor cho 68GB Game - Python Version
Chuyển đổi từ JavaScript sang Python để theo dõi WebSocket và gửi dữ liệu lên Firebase
"""

import json
import asyncio
import websockets
import requests
from datetime import datetime
import logging
import sys
from typing import Optional

# Cấu hình logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('68gb_monitor.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class GameWebSocketMonitor:
    """
    Class để theo dõi WebSocket và gửi dữ liệu game lên Firebase
    """
    
    def __init__(self, websocket_url: str, firebase_db_url: str):
        """
        Khởi tạo monitor
        
        Args:
            websocket_url: URL của WebSocket server cần theo dõi
            firebase_db_url: URL của Firebase Realtime Database
        """
        self.websocket_url = websocket_url
        self.firebase_db_url = firebase_db_url
        self.session = requests.Session()
        self.session.headers.update({'Content-Type': 'application/json'})
        
    def _send_to_firebase(self, session_type: str, data: str) -> bool:
        """
        Gửi dữ liệu lên Firebase Realtime Database
        
        Args:
            session_type: Loại phiên ("start" hoặc "end")
            data: Dữ liệu tin nhắn
            
        Returns:
            bool: True nếu gửi thành công, False nếu thất bại
        """
        try:
            # Tạo payload JSON giống như code JavaScript
            payload = {
                "time": datetime.now().isoformat(),  # Thời gian hiện tại (định dạng ISO)
                "type": session_type,                # Loại phiên (start/end)
                "data": data,                        # Dữ liệu tin nhắn
                "length": len(data)                  # Độ dài của tin nhắn
            }
            
            # Gửi dữ liệu lên Firebase
            firebase_endpoint = f"{self.firebase_db_url}/taixiu_sessions.json"
            response = self.session.post(firebase_endpoint, json=payload)
            
            if response.ok:
                logger.info(f"✅ Đã lưu phiên {session_type.upper()} vào Firebase")
                return True
            else:
                logger.error(f"❌ Lỗi lưu phiên {session_type.upper()}: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Lỗi khi gửi dữ liệu lên Firebase: {e}")
            return False
    
    def _process_message(self, message: str) -> None:
        """
        Xử lý tin nhắn từ WebSocket
        
        Args:
            message: Tin nhắn nhận được từ WebSocket
        """
        try:
            # Kiểm tra xem tin nhắn có chứa "mnmdsbgamestart" hoặc "mnmdsbgameend"
            if "mnmdsbgamestart" in message or "mnmdsbgameend" in message:
                # Xác định loại phiên: "start" (bắt đầu) hoặc "end" (kết thúc)
                session_type = "start" if "mnmdsbgamestart" in message else "end"
                
                # In thông tin phiên ra console (START hoặc END)
                logger.info(f"📥 PHIÊN {session_type.upper()}: {message}")
                
                # Gửi dữ liệu lên Firebase
                self._send_to_firebase(session_type, message)
                
        except Exception as e:
            logger.error(f"❌ Lỗi khi xử lý tin nhắn WebSocket: {e}")
    
    async def connect_and_monitor(self) -> None:
        """
        Kết nối đến WebSocket và bắt đầu theo dõi
        """
        logger.info(f"🔗 Đang kết nối đến WebSocket: {self.websocket_url}")
        
        try:
            async with websockets.connect(
                self.websocket_url,
                ping_interval=20,
                ping_timeout=10,
                close_timeout=10
            ) as websocket:
                logger.info("✅ Đã kết nối thành công đến WebSocket")
                
                async for message in websocket:
                    try:
                        # Xử lý tin nhắn nhận được
                        if isinstance(message, bytes):
                            # Nếu dữ liệu là bytes, chuyển đổi sang chuỗi UTF-8
                            text = message.decode('utf-8')
                        else:
                            # Nếu dữ liệu đã là chuỗi, sử dụng trực tiếp
                            text = str(message)
                        
                        # Xử lý tin nhắn
                        self._process_message(text)
                        
                    except Exception as e:
                        logger.error(f"❌ Lỗi khi xử lý tin nhắn: {e}")
                        continue
                        
        except websockets.exceptions.ConnectionClosed:
            logger.warning("⚠️ Kết nối WebSocket đã bị đóng")
        except Exception as e:
            logger.error(f"❌ Lỗi kết nối WebSocket: {e}")
    
    async def start_monitoring(self, reconnect_delay: int = 5) -> None:
        """
        Bắt đầu theo dõi với khả năng tự động kết nối lại
        
        Args:
            reconnect_delay: Thời gian chờ trước khi kết nối lại (giây)
        """
        logger.info("🚀 Bắt đầu theo dõi WebSocket...")
        
        while True:
            try:
                await self.connect_and_monitor()
            except KeyboardInterrupt:
                logger.info("⏹️ Dừng theo dõi theo yêu cầu người dùng")
                break
            except Exception as e:
                logger.error(f"❌ Lỗi không mong muốn: {e}")
            
            logger.info(f"🔄 Kết nối lại sau {reconnect_delay} giây...")
            await asyncio.sleep(reconnect_delay)


def main():
    """
    Hàm main để chạy monitor
    """
    # Định nghĩa URL của cơ sở dữ liệu Firebase Realtime Database
    FIREBASE_DB_URL = "https://data-real-time-68gb-default-rtdb.asia-southeast1.firebasedatabase.app"
    
    # URL WebSocket cần theo dõi (cần thay đổi theo WebSocket thực tế của game)
    # Thay thế URL này bằng URL WebSocket thực tế của game 68GB
    WEBSOCKET_URL = "https://xlih7bwjbmkump1kcvcz.cq.qnwxdhwica.com/query?querytype=12"  # ⚠️ CẦN CẬP NHẬT URL THỰC
    
    # Tạo và chạy monitor
    monitor = GameWebSocketMonitor(WEBSOCKET_URL, FIREBASE_DB_URL)
    
    try:
        asyncio.run(monitor.start_monitoring())
    except KeyboardInterrupt:
        logger.info("👋 Đã dừng chương trình")


if __name__ == "__main__":
    main()