<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <title>Chơi game SumClub | Game Portal</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <style>
    html, body {height:100%;margin:0;padding:0;overflow:hidden;}
    body {background:#0f172a;color:#fff;font-family:system-ui,-apple-system,Segoe UI,Roboto,Helvetica,Arial;}

    .game-container {position:relative;width:100vw;height:100vh;overflow:hidden;background:#0f172a;}
    .game-frame {width:100vw;height:100vh;border:0;display:block;visibility:hidden;}

/* Fallback khi site chặn embed (X-Frame-Options/CSP) */
.iframe-fallback {position:absolute;inset:0;display:none;z-index:160;align-items:center;justify-content:center;background:rgba(15,23,42,.96);text-align:center;padding:24px}
.iframe-fallback .box {max-width:560px;background:rgba(255,255,255,.04);border:1px solid rgba(255,255,255,.12);border-radius:14px;padding:18px 20px;box-shadow:0 10px 40px rgba(0,0,0,.35)}
.iframe-fallback h3 {margin:0 0 8px 0;font-size:18px}
.iframe-fallback p {margin:0 0 12px 0;opacity:.85}
.iframe-fallback a {display:inline-block;padding:10px 14px;border-radius:10px;background:#00bfff;color:#fff;text-decoration:none}

    /* Nút thoát và nút mở danh sách game */
    .top-buttons {position:fixed;top:15px;right:15px;z-index:120;display:flex;gap:10px}
    .btn {padding:10px 15px;border:none;border-radius:10px;cursor:pointer;font-weight:600;box-shadow:0 3px 18px #0006;}
    .btn-exit {background:#f87171;color:#fff;}
    .btn-exit:hover{background:#ef4444}
    .btn-switcher {background:#00bfff;color:#fff;}

    /* Robot UI (kéo thả cả cụm) */
    .robot-ui {position:absolute;left:24px;top:24px;z-index:150;cursor:grab;opacity:0;pointer-events:none;transition:opacity .2s ease;}

    /* Guard overlay trong lúc xác thực key */
    .guard {position:absolute;inset:0;background:rgba(15,23,42,.96);display:flex;align-items:center;justify-content:center;z-index:200}
    .guard-box {text-align:center;padding:20px 24px;border-radius:14px;background:rgba(255,255,255,.04);border:1px solid rgba(255,255,255,.12);box-shadow:0 10px 40px rgba(0,0,0,.35)}
    .spinner {width:28px;height:28px;border-radius:50%;border:3px solid rgba(255,255,255,.25);border-top-color:#38bdf8;animation:spin .9s linear infinite;margin:0 auto 12px}
    @keyframes spin {to{transform:rotate(360deg)}}
    .hint {opacity:.7;font-size:14px;margin-top:6px}

    /* === Mini Predictor (từ đoạn iframe ban đầu), gắn vào robot-ui === */
    .draggable-box {background:rgba(17,24,39,.9);border:1px solid rgba(255,255,255,.12);border-radius:14px;box-shadow:0 10px 25px rgba(0,0,0,.35);padding:12px 14px;min-width:220px;}
    .draggable-box .close-btn {position:absolute;right:6px;top:6px;background:transparent;border:none;color:#fff;font-size:18px;cursor:pointer;opacity:.8}
    .robot-container {display:flex;align-items:center;gap:10px}
    .robot-avatar {width:46px;height:46px;border-radius:50%;object-fit:cover;border:2px solid rgba(255,255,255,.2)}
    .prediction-box {margin-top:6px}
    .session-info {font-size:14px}
    .confidence {margin-top:6px;font-size:13px;opacity:.9}

    /* === Game Switcher (hiển thị NHIỀU game trong cùng khung bằng cách chuyển) === */
    .switcher-overlay {position:fixed;inset:0;background:rgba(0,0,0,.5);z-index:180;display:none;}
    .switcher {position:fixed;right:16px;top:64px;width:360px;max-height:70vh;overflow:auto;background:#0b1220;border:1px solid rgba(255,255,255,.1);border-radius:14px;z-index:190;display:none;}
    .switcher header {display:flex;align-items:center;justify-content:space-between;padding:10px 12px;border-bottom:1px solid rgba(255,255,255,.08)}
    .switcher header input {flex:1;margin-right:8px;padding:8px 10px;border-radius:10px;border:1px solid rgba(255,255,255,.12);background:#0f172a;color:#fff}
    .switcher header button {background:transparent;border:none;color:#fff;font-size:18px;cursor:pointer;opacity:.8}
    .switcher .list {padding:8px 8px 14px;display:grid;grid-template-columns:1fr;gap:8px}
    .switch-item {display:flex;align-items:center;justify-content:space-between;gap:10px;padding:10px 12px;border-radius:12px;background:#0f172a;border:1px solid rgba(255,255,255,.06)}
    .switch-item .title {font-weight:600}
    .switch-item .actions {display:flex;gap:6px}
    .switch-item a, .switch-item button {padding:6px 10px;border-radius:10px;border:1px solid rgba(255,255,255,.12);background:#111827;color:#fff;cursor:pointer;text-decoration:none}
    .switch-item a:hover, .switch-item button:hover {background:#172033}

    noscript {position:absolute;inset:0;background:#0f172a;color:#fff;display:flex;align-items:center;justify-content:center;padding:20px;text-align:center}
  </style>
</head>
<body>
  <div class="game-container" id="gameContainer">
    <!-- IFRAME: hiển thị game hiện tại; có thể chuyển game bằng switcher -->
    <iframe id="gameFrame" class="game-frame" src="https://play.sum34.club/" allowfullscreen></iframe>
<!-- Fallback overlay nếu iframe bị chặn -->

    <!-- Nút cố định trên cùng bên phải -->
    <div class="top-buttons">
      <button class="btn btn-switcher" id="btnOpenSwitcher">🎮 Game khác</button>
      <button class="btn btn-exit" onclick="window.location.href='https://tooltxvip.pro/user/game.php'">
        <i class="fa fa-sign-out-alt"></i> Thoát
      </button>
    </div>

    <!-- Robot UI: chứa mini predictor + (tùy game) file robot_* -->
    <div class="robot-ui" id="robotDrag">
      <script>
        // Runtime cho robot PHP/JS
        window.GW_RUNTIME = {
          BASE_URL: "https://tooltxvip.pro/",
          USER_ID: 389,
          GAME_ID: 8,
          GAME_NAME: "SumClub",
          GAME_SLUG: "sumclub",
          TOKEN_ENDPOINT: "https://tooltxvip.pro/api/get_token.php",
          DATA_ENDPOINT:  "https://tooltxvip.pro/api/api.php"
        };
      </script>

      <!-- 
      <div class="draggable-box" id="predictorBox">
        <button class="close-btn" onclick="document.getElementById('predictorBox').style.display='none'">×</button>
        <div class="robot-container">
          <img src="https://i.postimg.cc/1zLnNsCZ/IMG-3575.gif" alt="Robot" class="robot-avatar">
          <div class="session-info" id="session-info-text">Đang khởi động robot dự đoán...</div>
        </div>
        <div class="prediction-box" id="predictionBox">
          <div class="confidence" id="confidence-box" style="display:none;">Độ tin cậy: <span id="confidence">--%</span></div>
        </div>
      </div> -->

      <!-- load player component -->
<script src="https://unpkg.com/@dotlottie/player-component@latest/dist/dotlottie-player.mjs" type="module"></script>

<style>
.robot-box-sunwin {
    position: relative; width: 160px; min-height: 120px;
    user-select: none; background: transparent; touch-action: none;
    transform-origin: center center;
    transition: transform .35s ease;
}
.robot-box-sunwin .robot-img {
    width: 110px; height: 110px; display: block; margin: 0 auto;
}

/* Nút xoay */
.rotate-btn{
    position: absolute;
    left: -6px; top: -6px;
    width: 28px; height: 28px;
    border-radius: 999px;
    backdrop-filter: blur(4px);
    background: rgba(34,34,34,.85);
    color: #38bdf8; border: 1px solid #38bdf8;
    display: grid; place-items: center;
    font-size: 16px; line-height: 1;
    box-shadow: 0 6px 16px #0006;
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
    z-index: 5;
}
.rotate-btn:active{ transform: scale(.96); }

.robot-box-sunwin .bubble {
    position: absolute; left: 118px; top: 0;
    min-width: 120px; max-width: 190px;
    padding: 10px 16px 10px 18px;
    background: rgba(34,34,34,0.85); color: #fff;
    border-radius: 12px; font-size: 1em; font-weight: 500;
    border: 1.5px solid #38bdf8; box-shadow: 0 8px 24px #0005;
    display: flex; flex-direction: column; pointer-events: none;
}
.robot-box-sunwin .bubble strong { color: #38bdf8; }
.robot-box-sunwin .bubble .confidence { margin-top: 2px; color: #ffe066; font-size: .96em; font-weight: 600; }
</style>

<div class="robot-box-sunwin" id="robot-box-sunwin">
    <!-- Lottie Icon -->
    <dotlottie-player 
        class="robot-img"
        src="https://lottie.host/55ab9688-9a63-4f35-93f8-b40bd7fb8058/4n00MLJLZk.lottie" 
        background="transparent" 
        speed="1" 
        loop 
        autoplay>
    </dotlottie-player>

    <!-- Nút xoay -->
    <button class="rotate-btn" type="button" aria-label="Xoay icon">⤾</button>

    <!-- Bubble -->
    <div class="bubble" id="robot-bubble-sunwin">
        <div>Đang tải...</div>
    </div>
</div>

<script>
(() => {
    const box = document.getElementById('robot-box-sunwin');
    const rotateBtn = document.querySelector('.rotate-btn');
    const bubble = document.getElementById('robot-bubble-sunwin');

    // Xoay toàn bộ khung
    let deg = 0;
    function applyRotate(){ box.style.transform = `rotate(${deg}deg)`; }
    rotateBtn?.addEventListener('click', () => {
        deg = (deg + 90) % 360;
        applyRotate();
    });
    // Double-tap trên mobile
    let lastTap = 0;
    box?.addEventListener('touchend', () => {
        const now = Date.now();
        if (now - lastTap < 300){ deg = (deg + 90) % 360; applyRotate(); }
        lastTap = now;
    });

    // ==== API SumClub giữ nguyên như cũ ====
    const R = window.GW_RUNTIME || {};
    const FIXED_GAME_ID = 8;
    const GAME_SLUG = 'sumclub';

    let API_TOKEN = null;
    let lastRound = null;
    let updateTimeout = null;
    const delayMs = 12000;
    const pollMs  = 3000;

    const TOKEN_URL = R.TOKEN_ENDPOINT || ((R.BASE_URL || '/') + 'api/get_token.php');
    const DATA_URL  = R.DATA_ENDPOINT  || ((R.BASE_URL || '/') + 'api/api.php');

    function setHTML(html){ if (bubble) bubble.innerHTML = html; }
    function normTX(v){
        if (v == null) return '...';
        const s = String(v).trim().toLowerCase();
        if (["tai","tài","t","big","over","high","1",">10"].includes(s)) return 'Tài';
        if (["xiu","xỉu","x","small","under","low","0","<=10"].includes(s)) return 'Xỉu';
        return /tai|tài/.test(s) ? 'Tài' : (/xiu|xỉu/.test(s) ? 'Xỉu' : String(v));
    }
    function unwrap(d){ return (d && d.data) ? d.data : d; }
    function pickRound(d){
        const obj = unwrap(d);
        const r = (obj?.phien_hien_tai ?? obj?.next_session ?? obj?.phien ?? obj?.round ?? obj?.round_id ?? 0);
        const n = Number(r);
        return Number.isFinite(n) ? n : 0;
    }
    function pickResult(d){
        const obj = unwrap(d);
        return normTX(obj?.du_doan ?? obj?.prediction ?? obj?.ketqua ?? obj?.result ?? '...');
    }
    function pickConf(d){
        const obj = unwrap(d);
        let c = (obj?.do_tin_cay ?? obj?.confidence ?? obj?.probability);
        if (c == null) return '...';
        c = Number(c);
        if (!Number.isFinite(c)) return '...';
        if (c <= 1) c *= 100;
        return Math.round(c);
    }
    function renderBox(d){
        const round = pickRound(d);
        const res   = pickResult(d);
        const conf  = pickConf(d);
        const displayRound = Number.isFinite(round) ? round : '...';
        setHTML(`
            <div><strong>#${displayRound}: ${res}</strong></div>
            <div class="confidence">Độ tin cậy: <b>${conf}%</b></div>
        `);
    }
    async function fetchToken(){
        try{
            const res = await fetch(TOKEN_URL);
            const j = await res.json();
            if (j && (j.access_token || j.token)){
                API_TOKEN = j.access_token || j.token;
                return true;
            }
            setHTML('<div>Lỗi lấy access token!</div>');
            return false;
        }catch(_){
            setHTML('<div>Kết nối get_token lỗi!</div>');
            return false;
        }
    }
    async function updateBox(){
        if (!API_TOKEN){ setHTML('<div>Chưa có access token!</div>'); return; }
        const url = new URL(DATA_URL, window.location.origin);
        url.searchParams.set('game_id', FIXED_GAME_ID);
        url.searchParams.set('game_slug', GAME_SLUG);
        if (R.USER_ID) url.searchParams.set('user_id', R.USER_ID);

        try{
            const res = await fetch(url.toString(), {
                headers: { 'Authorization': 'Bearer ' + API_TOKEN }
            });
            const json = await res.json();

            const hasData = (json && (json.data || json.Phien || json.phien_hien_tai));
            if (hasData){
                const d = json.data || json;
                if (lastRound === null){
                    lastRound = pickRound(d);
                    renderBox(d);
                    return;
                }
                const cur = pickRound(d);
                if (Number.isFinite(cur) && Number.isFinite(lastRound) && cur > lastRound && !updateTimeout){
                    updateTimeout = setTimeout(() => {
                        lastRound = cur;
                        renderBox(d);
                        updateTimeout = null;
                        console.log('SumClub cập nhật phiên mới: #' + cur);
                    }, delayMs);
                } else {
                    renderBox(d);
                }
            } else {
                setHTML(`<div>${json && json.message ? json.message : 'Lỗi dữ liệu!'}</div>`);
            }
        }catch(_){
            setHTML('<div>Kết nối API lỗi!</div>');
        }
    }
    async function init(){
        if (!await fetchToken()) return;
        await updateBox();
        setInterval(updateBox, pollMs);
    }
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else { init(); }
})();
</script>
    </div>

    <!-- Guard overlay khi xác thực key -->
    <div class="guard" id="guard">
      <div class="guard-box">
        <div class="spinner"></div>
        <div><strong>Đang xác thực key…</strong></div>
        <div class="hint">Vui lòng đợi trong giây lát</div>
      </div>
    </div>

    <!-- Game Switcher Overlay / Panel -->
    <div class="switcher-overlay" id="switcherOverlay"></div>
    <div class="switcher" id="switcherPanel">
      <header>
        <input type="text" id="gameSearch" placeholder="Tìm game...">
        <button id="btnCloseSwitcher" title="Đóng">×</button>
      </header>
      <div class="list" id="switcherList">
                            <div class="switch-item" data-id="6" data-url="https://68gbvn25.biz/" data-name="68gamebai">
            <div class="title">68gamebai</div>
            <div class="actions">
              <!-- Mặc định: chuyển kèm reload để nạp đúng robot_*.php -->
              <a href="https://tooltxvip.pro/user/play_game.php?game_id=6">Mở</a>
              <!-- Không reload (chỉ đổi src iframe) – dành cho khi bạn dùng robot_default đa năng -->
            </div>
          </div>
                            <div class="switch-item" data-id="5" data-url="https://play.789.club/" data-name="789club">
            <div class="title">789club</div>
            <div class="actions">
              <!-- Mặc định: chuyển kèm reload để nạp đúng robot_*.php -->
              <a href="https://tooltxvip.pro/user/play_game.php?game_id=5">Mở</a>
              <!-- Không reload (chỉ đổi src iframe) – dành cho khi bạn dùng robot_default đa năng -->
            </div>
          </div>
                            <div class="switch-item" data-id="3" data-url="http://localhost/duygame/user/robots/robot_b52.php" data-name="B52">
            <div class="title">B52</div>
            <div class="actions">
              <!-- Mặc định: chuyển kèm reload để nạp đúng robot_*.php -->
              <a href="https://tooltxvip.pro/user/play_game.php?game_id=3">Mở</a>
              <!-- Không reload (chỉ đổi src iframe) – dành cho khi bạn dùng robot_default đa năng -->
            </div>
          </div>
                            <div class="switch-item" data-id="2" data-url="http://localhost/duygame/user/robots/robot_hitclub.php" data-name="HitMd5">
            <div class="title">HitMd5</div>
            <div class="actions">
              <!-- Mặc định: chuyển kèm reload để nạp đúng robot_*.php -->
              <a href="https://tooltxvip.pro/user/play_game.php?game_id=2">Mở</a>
              <!-- Không reload (chỉ đổi src iframe) – dành cho khi bạn dùng robot_default đa năng -->
            </div>
          </div>
                            <div class="switch-item" data-id="11" data-url="https://h02lns5u.md5edu2.com/home/<USER>/" data-name="LuckyWin">
            <div class="title">LuckyWin</div>
            <div class="actions">
              <!-- Mặc định: chuyển kèm reload để nạp đúng robot_*.php -->
              <a href="https://tooltxvip.pro/user/play_game.php?game_id=11">Mở</a>
              <!-- Không reload (chỉ đổi src iframe) – dành cho khi bạn dùng robot_default đa năng -->
            </div>
          </div>
                            <div class="switch-item" data-id="4" data-url="https://play.rikvip.win/" data-name="Rikvip">
            <div class="title">Rikvip</div>
            <div class="actions">
              <!-- Mặc định: chuyển kèm reload để nạp đúng robot_*.php -->
              <a href="https://tooltxvip.pro/user/play_game.php?game_id=4">Mở</a>
              <!-- Không reload (chỉ đổi src iframe) – dành cho khi bạn dùng robot_default đa năng -->
            </div>
          </div>
                            <div class="switch-item" data-id="7" data-url="https://68gbvn25.biz/" data-name="Sicbo68gb">
            <div class="title">Sicbo68gb</div>
            <div class="actions">
              <!-- Mặc định: chuyển kèm reload để nạp đúng robot_*.php -->
              <a href="https://tooltxvip.pro/user/play_game.php?game_id=7">Mở</a>
              <!-- Không reload (chỉ đổi src iframe) – dành cho khi bạn dùng robot_default đa năng -->
            </div>
          </div>
                            <div class="switch-item" data-id="10" data-url="https://play.789.club/" data-name="Sicbo789">
            <div class="title">Sicbo789</div>
            <div class="actions">
              <!-- Mặc định: chuyển kèm reload để nạp đúng robot_*.php -->
              <a href="https://tooltxvip.pro/user/play_game.php?game_id=10">Mở</a>
              <!-- Không reload (chỉ đổi src iframe) – dành cho khi bạn dùng robot_default đa năng -->
            </div>
          </div>
                            <div class="switch-item" data-id="9" data-url="https://web.sun.win/" data-name="SicboSun">
            <div class="title">SicboSun</div>
            <div class="actions">
              <!-- Mặc định: chuyển kèm reload để nạp đúng robot_*.php -->
              <a href="https://tooltxvip.pro/user/play_game.php?game_id=9">Mở</a>
              <!-- Không reload (chỉ đổi src iframe) – dành cho khi bạn dùng robot_default đa năng -->
            </div>
          </div>
                            <div class="switch-item" data-id="12" data-url="https://web.sun.win" data-name="Sicbosun88">
            <div class="title">Sicbosun88</div>
            <div class="actions">
              <!-- Mặc định: chuyển kèm reload để nạp đúng robot_*.php -->
              <a href="https://tooltxvip.pro/user/play_game.php?game_id=12">Mở</a>
              <!-- Không reload (chỉ đổi src iframe) – dành cho khi bạn dùng robot_default đa năng -->
            </div>
          </div>
                            <div class="switch-item" data-id="8" data-url="https://play.sum34.club/" data-name="SumClub">
            <div class="title">SumClub · <small>(đang chơi)</small></div>
            <div class="actions">
              <!-- Mặc định: chuyển kèm reload để nạp đúng robot_*.php -->
              <a href="https://tooltxvip.pro/user/play_game.php?game_id=8">Mở</a>
              <!-- Không reload (chỉ đổi src iframe) – dành cho khi bạn dùng robot_default đa năng -->
            </div>
          </div>
                            <div class="switch-item" data-id="1" data-url="https://web.sun.win/" data-name="Sunwin">
            <div class="title">Sunwin</div>
            <div class="actions">
              <!-- Mặc định: chuyển kèm reload để nạp đúng robot_*.php -->
              <a href="https://tooltxvip.pro/user/play_game.php?game_id=1">Mở</a>
              <!-- Không reload (chỉ đổi src iframe) – dành cho khi bạn dùng robot_default đa năng -->
            </div>
          </div>
              </div>
    </div>

    <noscript>Trang này cần JavaScript để xác thực key & thiết bị. Vui lòng bật JavaScript.</noscript>
  </div>

  <script>
    // ==== Cấu hình: nếu bạn dùng robot_default cho mọi game, có thể bật chuyển không reload.
    const SWITCH_WITH_RELOAD = true; // true: điều hướng URL; false: chỉ đổi iframe src

    // ======== Helper: cùng format với trang chọn game để trùng device_name ========
    function getDeviceName() {
      let ua = navigator.userAgent;
      let platform = navigator.platform || '';
      let device = '';
      if (/android/i.test(ua)) device = 'Android';
      else if (/iPad|iPhone|iPod/.test(ua)) device = 'iOS';
      else if (/windows/i.test(ua)) device = 'Windows';
      else if (/macintosh/i.test(ua)) device = 'MacOS';
      else if (/linux/i.test(ua)) device = 'Linux';
      else device = 'Other';

      let browser = '';
      let ual = ua.toLowerCase();
      if (ual.indexOf("chrome") > -1) browser = 'Chrome';
      else if (ual.indexOf("firefox") > -1) browser = 'Firefox';
      else if (ual.indexOf("safari") > -1 && device.indexOf("iOS") == -1) browser = 'Safari';
      else if (ual.indexOf("edge") > -1) browser = 'Edge';
      else browser = 'Other';

      let hash = btoa(ua + platform).substring(0,12);
      return device + ' ' + browser + ' (' + hash + ')';
    }

    const USER_ID = 389;
    const GAME_ID = 8;

    function getUniversalKey(devName) {
      const uniKeyName = `universal_key_${USER_ID}_${devName}`;
      const legacyKeyName = `game_key_${GAME_ID}_${devName}`;
      let key = localStorage.getItem(uniKeyName);
      if (key && key.trim() !== '') return key.trim();
      let legacy = localStorage.getItem(legacyKeyName);
      if (legacy && legacy.trim() !== '') {
        localStorage.setItem(uniKeyName, legacy.trim());
        return legacy.trim();
      }
      return '';
    }

    async function verifyAndStart() {
      const device = getDeviceName();
      const key = getUniversalKey(device);
      const guard = document.getElementById('guard');
      const robot = document.getElementById('robotDrag');
      const frame = document.getElementById('gameFrame');

      if (!key) {
        window.location.href = 'https://tooltxvip.pro/user/game.php?need_key=1';
        return;
      }

      try {
        const res = await fetch('../api/check_key.php', {
          method: 'POST',
          headers: {'Content-Type':'application/x-www-form-urlencoded'},
          body: 'game_id=' + encodeURIComponent(GAME_ID)
              + '&key=' + encodeURIComponent(key)
              + '&device_name=' + encodeURIComponent(device)
        });
        let data = {};
        try { data = await res.json(); } catch(e){}

        if (data && (data.success === 1 || data.success === true)) {
          if (guard) guard.remove();
          if (frame) { frame.style.visibility = 'visible'; initIframeWatch(frame.src); }
          if (robot) { robot.style.opacity = '1'; robot.style.pointerEvents = 'auto'; }
          // Khởi động vòng hiển thị dự đoán demo
          startPredictorCycle();
        } else {
          localStorage.removeItem(`universal_key_${USER_ID}_${device}`);
          localStorage.removeItem(`game_key_${GAME_ID}_${device}`);
          window.location.href = 'https://tooltxvip.pro/user/game.php?recheck=1';
        }
      } catch (err) {
        window.location.href = 'https://tooltxvip.pro/user/game.php?err=network';
      }
    }

    // Kéo thả toàn bộ khối robot-ui trong phạm vi game-container
    (function(){
      const robot = document.getElementById('robotDrag');
      const container = document.getElementById('gameContainer');
      if (!robot || !container) return;
      let isDown = false, offsetX = 0, offsetY = 0;

      robot.onmousedown = function(e){
        isDown = true;
        offsetX = e.clientX - robot.offsetLeft;
        offsetY = e.clientY - robot.offsetTop;
        robot.style.cursor = "grabbing";
        document.body.style.userSelect = "none";
      };
      document.onmousemove = function(e){
        if(!isDown) return;
        let rect = container.getBoundingClientRect();
        let minX = 0, minY = 0;
        let maxX = rect.width - robot.offsetWidth;
        let maxY = rect.height - robot.offsetHeight;
        let x = e.clientX - rect.left - offsetX;
        let y = e.clientY - rect.top - offsetY;
        x = Math.max(minX, Math.min(maxX, x));
        y = Math.max(minY, Math.min(maxY, y));
        robot.style.left = x + "px";
        robot.style.top = y + "px";
      };
      document.onmouseup = function(){
        isDown = false;
        robot.style.cursor = "grab";
        document.body.style.userSelect = "";
      };
      robot.ontouchstart = function(e){
        isDown = true; let t = e.touches[0]; let r = robot.getBoundingClientRect();
        offsetX = t.clientX - r.left; offsetY = t.clientY - r.top;
      };
      document.ontouchmove = function(e){
        if(!isDown) return; let t = e.touches[0]; let rect = container.getBoundingClientRect();
        let x = t.clientX - rect.left - offsetX; let y = t.clientY - rect.top - offsetY;
        x = Math.max(0, Math.min(rect.width - robot.offsetWidth, x));
        y = Math.max(0, Math.min(rect.height - robot.offsetHeight, y));
        robot.style.left = x + 'px'; robot.style.top = y + 'px';
      };
      document.ontouchend = function(){ isDown = false; };
    })();

    // ==== Kiểm tra iframe có bị chặn không & hiển thị fallback ====
    function initIframeWatch(url){
      const frame = document.getElementById('gameFrame');
      const fallback = document.getElementById('iframeFallback');
      const link = document.getElementById('fallbackOpenLink');
      if (link && url) link.href = url;
      if (!frame || !fallback) return;
      fallback.style.display = 'none';
      let loaded = false;
      const onLoad = ()=>{ loaded = true; fallback.style.display = 'none'; };
      frame.addEventListener('load', onLoad, { once: true });
      setTimeout(()=>{ if(!loaded){ fallback.style.display = 'flex'; } }, 3000);
    }

    // ==== Mini predictor demo logic (tối giản, bạn thay bằng API thực tế) ====
    const INITIAL_DELAY_MS = 2500;
    const PREDICTION_DISPLAY_MS = 45000;
    function startPredictorCycle(){
      const sessionText = document.getElementById('session-info-text');
      const confidenceBox = document.getElementById('confidence-box');
      const confidenceValue = document.getElementById('confidence');

      function runOnce(){
        setTimeout(()=>{
          if (!sessionText) return;
          sessionText.textContent = 'Đang phân tích ván tiếp theo...';
          confidenceBox.style.display = 'block';
          let pct = Math.floor(40 + Math.random()*20);
          confidenceValue.textContent = pct + '%';
          const iv = setInterval(()=>{
            pct = Math.min(99, pct + Math.floor(Math.random()*7));
            confidenceValue.textContent = pct + '%';
          }, 1500);
          setTimeout(()=>{
            clearInterval(iv);
            sessionText.textContent = 'Gợi ý đã sẵn sàng!';
            setTimeout(()=>{
              sessionText.textContent = 'Đang chờ ván mới...';
              confidenceBox.style.display = 'none';
              // Lặp lại chu kỳ dự đoán tiếp theo
              runOnce();
            }, 4000);
          }, PREDICTION_DISPLAY_MS);
        }, INITIAL_DELAY_MS);
      }
      runOnce();
    }

    // ==== Game Switcher logic ====
    const btnOpenSwitcher = document.getElementById('btnOpenSwitcher');
    const btnCloseSwitcher = document.getElementById('btnCloseSwitcher');
    const switcherPanel = document.getElementById('switcherPanel');
    const switcherOverlay = document.getElementById('switcherOverlay');
    const switcherList = document.getElementById('switcherList');
    const gameSearch = document.getElementById('gameSearch');

    function openSwitcher(){ switcherPanel.style.display='block'; switcherOverlay.style.display='block'; gameSearch?.focus(); }
    function closeSwitcher(){ switcherPanel.style.display='none'; switcherOverlay.style.display='none'; }

    btnOpenSwitcher.addEventListener('click', openSwitcher);
    btnCloseSwitcher.addEventListener('click', closeSwitcher);
    switcherOverlay.addEventListener('click', closeSwitcher);

    // Lọc danh sách
    gameSearch?.addEventListener('input', (e)=>{
      const q = e.target.value.toLowerCase();
      const items = switcherList.querySelectorAll('.switch-item');
      items.forEach(it=>{
        const name = (it.dataset.name||'').toLowerCase();
        it.style.display = name.includes(q) ? '' : 'none';
      });
    });

    // Nút "Xem nhanh" (không reload) – phù hợp khi bạn dùng robot_default đa năng
    switcherList.addEventListener('click', (e)=>{
      const btn = e.target.closest('.btnQuick');
      if (!btn) return;
      const item = btn.closest('.switch-item');
      const url = item.dataset.url;
      const name = item.dataset.name;
      if (!url) return;
      if (SWITCH_WITH_RELOAD) {
        // Điều hướng URL, để PHP include đúng robot_* tương ứng game
        const gid = item.dataset.id;
        window.location.href = 'https://tooltxvip.pro/user/play_game.php?game_id=' + encodeURIComponent(gid);
      } else {
        // Chỉ đổi src của iframe (robot_default nên đủ dùng cho mọi game)
        const frame = document.getElementById('gameFrame');
        frame.src = url;
        document.title = 'Chơi game ' + name + ' | Game Portal';
        initIframeWatch(url);
        closeSwitcher();
      }
    });

    // Khởi chạy xác thực trước khi hiển thị game
    document.addEventListener('DOMContentLoaded', verifyAndStart);
  </script>
</body>
</html>
