import asyncio
import websockets
import json
import threading
import time
from flask import Flask, jsonify, render_template_string
from flask_cors import CORS
import os

# Import thuật to<PERSON> dự đo<PERSON> (cần phải tạo file thuatoan.py tương ứng)
# from thuatoan import MasterPredictor
# predictor = MasterPredictor()

app = Flask(__name__)
CORS(app)
PORT = int(os.environ.get('PORT', 5000))

api_response_data = {
    "id": "@dirmemay",
    "Phien": None,
    "Xuc_xac1": None,
    "Xuc_xac2": None,
    "Xuc_xac3": None,
    "Tổng": None,
    "Phien_du_doan": None,
    "Du_doan": "?"
}

last_prediction = None
full_history = []
current_session_id = None

WEBSOCKET_URL = "wss://websocket.azhkthg1.net/websocket?token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhbW91bnQiOjAsInVzZXJuYW1lIjoiU0NfYXBpc3Vud2luMTIzIn0.hgrRbSV6vnBwJMg9ZFtbx3rRu9mX_hZMZ_m5gMNhkw0"
WS_HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Origin": "https://play.sun.win"
}
RECONNECT_DELAY = 2.5
PING_INTERVAL = 15

initial_messages = [
    [1, "MiniGame", "GM_dcmshiffsdf", "12123p", {
        "info": "{\"ipAddress\":\"2405:4802:18ce:a780:8c30:666c:5bfd:36b1\",\"wsToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.vDdq-SLgdXjRwijNY5PEMUEETEP4dQRklZnWcTtJML8\",\"locale\":\"vi\",\"userId\":\"59f3d05c-c4fc-4191-8259-88e68e2a8f0f\",\"username\":\"GM_dcmshiffdsf\"}",
        "signature": "05F08CF241C76DA35BB0C4F951181A807E2423EDB9FF99F9A24ABF6929E668889BB84BC1EE0DFE61F0114CE262D61DEBFFFA8E9DF09CA1E1985B326CAE963138027D37B13D7671545DCDD357079FFC7B18E2E33FC85D68E43571BC8D2CC28BC502D0D8FEE4544D680817F607309C415A6C496C287E44C98E91D04577DCA9CCFB"
    }],
    [6, "MiniGame", "taixiuPlugin", {"cmd": 1005}],
    [6, "MiniGame", "lobbyPlugin", {"cmd": 10001}]
]

websocket = None
ping_task = None
correct_predictions = 0
total_predictions = 0

class SimplePredictor:
    """Placeholder cho thuật toán dự đoán"""
    def __init__(self):
        self.history = []
    
    async def update_data(self, data):
        self.history.append(data)
        if len(self.history) > 100:  # Giữ lại 100 phiên gần nhất
            self.history.pop(0)
    
    async def predict(self):
        # Logic dự đoán đơn giản - có thể thay thế bằng thuật toán phức tạp hơn
        if len(self.history) < 3:
            return {"prediction": "?"}
        
        # Đếm số lần Tài/Xỉu trong 10 phiên gần nhất
        recent_results = self.history[-10:]
        tai_count = sum(1 for r in recent_results if r['result'] == 'Tài')
        xiu_count = len(recent_results) - tai_count
        
        # Dự đoán ngược lại với xu hướng gần đây
        if tai_count > xiu_count:
            return {"prediction": "Xỉu"}
        else:
            return {"prediction": "Tài"}

predictor = SimplePredictor()

async def send_ping(websocket):
    """Gửi ping định kỳ"""
    while True:
        try:
            await asyncio.sleep(PING_INTERVAL)
            if websocket and not websocket.closed:
                await websocket.ping()
                print('[📶] Ping sent.')
        except Exception as e:
            print(f'[❌] Ping error: {e}')
            break

async def process_message(message):
    """Xử lý message từ WebSocket"""
    global last_prediction, current_session_id, correct_predictions, total_predictions
    
    try:
        data = json.loads(message)
        if not isinstance(data, list) or len(data) < 2 or not isinstance(data[1], dict):
            return
        
        msg_data = data[1]
        cmd = msg_data.get('cmd')
        sid = msg_data.get('sid')
        d1 = msg_data.get('d1')
        d2 = msg_data.get('d2')
        d3 = msg_data.get('d3')
        gBB = msg_data.get('gBB')
        
        if cmd == 1008 and sid:
            api_response_data['Phien_du_doan'] = sid
        
        if cmd == 1003 and gBB:
            if not d1 or not d2 or not d3:
                return
            
            total = d1 + d2 + d3
            result = "Tài" if total > 10 else "Xỉu"
            
            correctness_status = None
            if last_prediction and last_prediction != "?":
                if last_prediction == result:
                    correct_predictions += 1
                    correctness_status = "ĐÚNG"
                else:
                    correctness_status = "SAI"
                total_predictions += 1
            
            await predictor.update_data({"result": result, "score": total})
            
            prediction_result = await predictor.predict()
            
            api_response_data['Phien'] = current_session_id
            api_response_data['Xuc_xac1'] = d1
            api_response_data['Xuc_xac2'] = d2
            api_response_data['Xuc_xac3'] = d3
            api_response_data['Tổng'] = total
            api_response_data['Du_doan'] = prediction_result['prediction']
            
            last_prediction = api_response_data['Du_doan']
            current_session_id = api_response_data['Phien_du_doan']
            
            win_rate = "0%" if total_predictions == 0 else f"{(correct_predictions / total_predictions * 100):.0f}%"
            print(f"Phiên #{api_response_data['Phien']}: {total} ({result}) | Dự đoán #{api_response_data['Phien_du_doan']}: {api_response_data['Du_doan']} | Tỷ lệ thắng: {win_rate}")
            
            full_history.append({
                'phien': api_response_data['Phien'],
                'xuc_xac_1': d1,
                'xuc_xac_2': d2,
                'xuc_xac_3': d3,
                'tong': total,
                'ket_qua': result,
                'du_doan': last_prediction,
                'trang_thai': correctness_status
            })
            
    except Exception as e:
        print(f'[❌] Lỗi xử lý message: {e}')

async def connect_websocket():
    """Kết nối và duy trì WebSocket"""
    global websocket, ping_task
    
    while True:
        try:
            print('[🔌] Đang kết nối WebSocket...')
            websocket = await websockets.connect(
                WEBSOCKET_URL,
                extra_headers=WS_HEADERS,
                ping_interval=None  # Tự quản lý ping
            )
            
            print('[✅] WebSocket connected.')
            
            # Gửi các message khởi tạo
            for i, msg in enumerate(initial_messages):
                await asyncio.sleep(i * 0.6)  # 600ms delay
                await websocket.send(json.dumps(msg))
            
            # Bắt đầu ping task
            ping_task = asyncio.create_task(send_ping(websocket))
            
            # Lắng nghe messages
            async for message in websocket:
                await process_message(message)
                
        except websockets.exceptions.ConnectionClosed:
            print('[🔌] WebSocket disconnected. Reconnecting...')
            if ping_task:
                ping_task.cancel()
            await asyncio.sleep(RECONNECT_DELAY)
        except Exception as e:
            print(f'[❌] WebSocket error: {e}')
            if ping_task:
                ping_task.cancel()
            await asyncio.sleep(RECONNECT_DELAY)

def start_websocket_loop():
    """Chạy WebSocket trong thread riêng"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    loop.run_until_complete(connect_websocket())

# Flask routes
@app.route('/sunlon')
def sunlon():
    """API endpoint trả về dữ liệu JSON"""
    return jsonify(api_response_data)

@app.route('/history')
def history():
    """API endpoint hiển thị lịch sử"""
    html_template = '''
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #1a1a1d; color: #c5c6c7; padding: 20px; line-height: 1.6; }
        .container { max-width: 700px; margin: auto; }
        h2 { color: #66fcf1; text-align: center; margin-bottom: 25px; border-bottom: 2px solid #45a29e; padding-bottom: 10px; font-weight: 300; letter-spacing: 1px; }
        .entry { background-color: #2c3e50; border: 1px solid #34495e; border-left: 5px solid #66fcf1; padding: 15px 20px; margin-bottom: 15px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); display: flex; flex-direction: column; gap: 8px; }
        .entry div { display: flex; align-items: center; }
        .label { color: #95a5a6; min-width: 100px; }
        .value { font-weight: bold; }
        .result-tai { color: #2ecc71; }
        .result-xiu { color: #e74c3c; }
        .status-dung { color: #2ecc71; }
        .status-sai { color: #e74c3c; }
        .dice-numbers { display: flex; gap: 5px; font-size: 1.2em; font-weight: bold; }
        .dice-number { padding: 4px 8px; border-radius: 4px; background-color: #555; color: #fff; }
        .total-score { color: #bdc3c7; margin-left: 8px; font-size: 0.9em; }
    </style>
    <div class="container">
        <h2>🎯 LỊCH SỬ {{ history_count }} PHIÊN GẦN NHẤT</h2>
        {% if history_list|length == 0 %}
            <p style="text-align: center;">Chưa có dữ liệu lịch sử.</p>
        {% else %}
            {% for h in history_list|reverse %}
            <div class="entry">
                <div><span class="label">Phiên:</span> <span class="value">#{{ h.phien }}</span></div>
                {% if h.du_doan and h.du_doan != "?" %}
                <div>
                    <span class="label">Dự đoán:</span> 
                    <span class="value">{{ h.du_doan }}</span>
                    {% if h.trang_thai == "ĐÚNG" %}
                        <span class="status-dung value">✅ ĐÚNG</span>
                    {% elif h.trang_thai == "SAI" %}
                        <span class="status-sai value">❌ SAI</span>
                    {% endif %}
                </div>
                {% endif %}
                <div>
                    <span class="label">Kết quả:</span> 
                    <span class="{% if h.ket_qua == 'Tài' %}result-tai{% else %}result-xiu{% endif %} value">{{ h.ket_qua }}</span>
                </div>
                <div class="dice-container">
                    <span class="label">Xúc xắc:</span>
                    <div class="dice-numbers">
                        <span class="dice-number">{{ h.xuc_xac_1 }}</span>
                        <span class="dice-number">{{ h.xuc_xac_2 }}</span>
                        <span class="dice-number">{{ h.xuc_xac_3 }}</span>
                    </div>
                    <span class="total-score">(Tổng: {{ h.tong }})</span>
                </div>
            </div>
            {% endfor %}
        {% endif %}
    </div>
    '''
    
    return render_template_string(html_template, 
                                history_count=len(full_history),
                                history_list=full_history)

@app.route('/')
def index():
    """Trang chủ"""
    return '''
    <h2>🎯 API Phân Tích Sunwin Tài Xỉu</h2>
    <p>Xem kết quả JSON: <a href="/sunlon">/sunlon</a></p>
    <p>Xem lịch sử các phiên: <a href="/history">/history</a></p>
    '''

if __name__ == '__main__':
    # Khởi động WebSocket trong thread riêng
    websocket_thread = threading.Thread(target=start_websocket_loop, daemon=True)
    websocket_thread.start()
    
    print(f'[🌐] Server is running at http://localhost:{PORT}')
    app.run(host='0.0.0.0', port=PORT, debug=False)